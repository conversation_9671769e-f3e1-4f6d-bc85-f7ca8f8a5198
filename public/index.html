<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国际运价管理系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>国际运价管理系统</h1>
            <div class="header-controls">
                <div class="data-toggle">
                    <button id="currentDataBtn" class="toggle-btn active">最新数据</button>
                    <button id="historicalDataBtn" class="toggle-btn">历史数据</button>
                </div>
                <button id="settingsBtn" class="settings-btn">区域配置</button>
                <button id="importBtn" class="import-btn">导入数据</button>
                <input type="file" id="importFile" style="display: none;" accept=".csv">
                <button id="addNewBtn" class="add-btn">新增运价</button>
            </div>
        </header>

        <div class="search-section">
            <div class="search-filters">
                <input type="text" id="gradeFilter" placeholder="搜索货品...">
                <input type="text" id="quantityFilter" placeholder="搜索货量...">
                <input type="text" id="portFilter" placeholder="搜索装卸港口...">
                <input type="text" id="dateFilter" placeholder="搜索日期...">
                <select id="regionFilter">
                    <option value="">所有区域</option>
                    <!-- 区域选项将通过JavaScript动态加载 -->
                </select>
                <button id="clearFilters" class="clear-btn">清除筛选</button>
            </div>
        </div>

        <div class="table-container">
            <table id="freightTable">
                <thead>
                    <tr>
                        <th>船名 (VESSEL)</th>
                        <th>租家 (CHARTERER)</th>
                        <th>受载期 (LAYCAN)</th>
                        <th>装港 (LOAD)</th>
                        <th>卸港 (DISCHARGE)</th>
                        <th>货品 (GRADE)</th>
                        <th>货量 (QUANTITY)</th>
                        <th>运费 (FREIGHT USD PMT)</th>
                        <th>状态 (STATUS)</th>
                        <th>装港区域</th>
                        <th>卸港区域</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- Pagination Controls -->
        <div id="paginationControls" class="pagination-controls">
            <button id="prevPageBtn" class="pagination-btn">上一页</button>
            <span id="pageInfo"></span>
            <button id="nextPageBtn" class="pagination-btn">下一页</button>
            <select id="pageSizeSelect">
                <option value="30" selected>30 / 页</option>
                <option value="50">50 / 页</option>
            </select>
        </div>
    </div>

    <!-- 侧滑面板 -->
    <div id="slidePanel" class="slide-panel">
        <div class="panel-content">
            <div class="panel-header">
                <h2 id="panelTitle">新增运价</h2>
                <button id="closePanelBtn" class="close-btn">&times;</button>
            </div>
            <form id="freightForm">
                <div class="form-group">
                    <label for="vessel">船名 (VESSEL):</label>
                    <input type="text" id="vessel" name="vessel" required>
                </div>
                <div class="form-group">
                    <label for="charterer">租家 (CHARTERER):</label>
                    <input type="text" id="charterer" name="charterer" required>
                </div>
                <div class="form-group">
                    <label for="laycan">受载期 (LAYCAN):</label>
                    <input type="text" id="laycan" name="laycan" placeholder="例: 24-26 AUG" required>
                </div>
                <div class="form-group">
                    <label for="load">装港 (LOAD):</label>
                    <input type="text" id="load" name="load" required>
                </div>
                <div class="form-group">
                    <label for="discharge">卸港 (DISCHARGE):</label>
                    <input type="text" id="discharge" name="discharge" required>
                </div>
                <div class="form-group">
                    <label for="grade">货品 (GRADE):</label>
                    <input type="text" id="grade" name="grade" placeholder="例: MTBE, SAF, BIOS" required>
                </div>
                <div class="form-group">
                    <label for="quantity">货量 (QUANTITY):</label>
                    <input type="text" id="quantity" name="quantity" placeholder="例: 35-40KT" required>
                </div>
                <div class="form-group">
                    <label for="freight">运费 (FREIGHT USD PMT):</label>
                    <input type="text" id="freight" name="freight" placeholder="例: $140 PMT" required>
                </div>
                <div class="form-group">
                    <label for="status">状态 (STATUS):</label>
                    <select id="status" name="status" required>
                        <option value="">请选择状态</option>
                        <option value="ACTIVE">ACTIVE</option>
                        <option value="PENDING">PENDING</option>
                        <option value="COMPLETED">COMPLETED</option>
                        <option value="CANCELLED">CANCELLED</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="load_region">装港区域:</label>
                    <select id="load_region" name="load_region" required>
                        <option value="">请选择区域</option>
                        <!-- 区域选项将通过JavaScript动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="discharge_region">卸港区域:</label>
                    <select id="discharge_region" name="discharge_region" required>
                        <option value="">请选择区域</option>
                        <!-- 区域选项将通过JavaScript动态加载 -->
                    </select>
                </div>
                <div class="form-actions">
                    <button type="submit" class="save-btn">保存</button>
                    <button type="button" id="cancelBtn" class="cancel-btn">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 区域配置面板 -->
    <div id="settingsPanel" class="settings-panel">
        <div class="panel-content">
            <div class="panel-header">
                <h2>区域配置</h2>
                <button id="closeSettingsBtn" class="close-btn">&times;</button>
            </div>

            <div class="form-group">
                <label>当前区域列表:</label>
                <div id="regionList" class="region-list">
                    <!-- 区域列表将通过JavaScript动态加载 -->
                </div>
            </div>

            <div class="form-group">
                <label>添加新区域:</label>
                <div class="add-region-form">
                    <input type="text" id="newRegionInput" class="add-region-input" placeholder="输入新区域名称">
                    <button id="addRegionBtn" class="add-region-btn">添加</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
