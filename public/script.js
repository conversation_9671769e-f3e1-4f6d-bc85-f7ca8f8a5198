class FreightRateManager {
    constructor() {
        this.data = [];
        this.filteredData = [];
        this.currentView = 'current'; // 'current' or 'historical'
        this.editingId = null;
        this.regions = [];
        this.currentPage = 1;
        this.pageSize = 30;

        this.initializeElements();
        this.bindEvents();
        this.loadRegions();
        this.loadData();
    }

    initializeElements() {
        // 按钮元素
        this.currentDataBtn = document.getElementById('currentDataBtn');
        this.historicalDataBtn = document.getElementById('historicalDataBtn');
        this.addNewBtn = document.getElementById('addNewBtn');
        this.closePanelBtn = document.getElementById('closePanelBtn');
        this.cancelBtn = document.getElementById('cancelBtn');
        this.clearFiltersBtn = document.getElementById('clearFilters');
        this.settingsBtn = document.getElementById('settingsBtn');
        this.closeSettingsBtn = document.getElementById('closeSettingsBtn');
        this.addRegionBtn = document.getElementById('addRegionBtn');

        // 搜索元素
        this.gradeFilter = document.getElementById('gradeFilter');
        this.quantityFilter = document.getElementById('quantityFilter');
        this.portFilter = document.getElementById('portFilter');
        this.dateFilter = document.getElementById('dateFilter');
        this.regionFilter = document.getElementById('regionFilter');

        // 表格和面板元素
        this.tableBody = document.getElementById('tableBody');
        this.slidePanel = document.getElementById('slidePanel');
        this.freightForm = document.getElementById('freightForm');
        this.panelTitle = document.getElementById('panelTitle');
        this.settingsPanel = document.getElementById('settingsPanel');
        this.regionList = document.getElementById('regionList');
        this.newRegionInput = document.getElementById('newRegionInput');
        this.loadRegionSelect = document.getElementById('load_region');
                this.dischargeRegionSelect = document.getElementById('discharge_region');

        // Pagination elements
        this.paginationControls = document.getElementById('paginationControls');
        this.prevPageBtn = document.getElementById('prevPageBtn');
        this.nextPageBtn = document.getElementById('nextPageBtn');
        this.pageInfo = document.getElementById('pageInfo');
                this.pageSizeSelect = document.getElementById('pageSizeSelect');

        // Import elements
        this.importBtn = document.getElementById('importBtn');
        this.importFile = document.getElementById('importFile');
    }

    bindEvents() {
        // 数据切换按钮
        this.currentDataBtn.addEventListener('click', () => this.switchView('current'));
        this.historicalDataBtn.addEventListener('click', () => this.switchView('historical'));

        // 面板控制
        this.addNewBtn.addEventListener('click', () => this.openPanel());
        this.closePanelBtn.addEventListener('click', () => this.closePanel());
        this.cancelBtn.addEventListener('click', () => this.closePanel());
        this.settingsBtn.addEventListener('click', () => this.openSettingsPanel());
        this.closeSettingsBtn.addEventListener('click', () => this.closeSettingsPanel());

        // 区域管理
        this.addRegionBtn.addEventListener('click', () => this.addRegion());
        this.newRegionInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.addRegion();
        });

        // 表单提交
        this.freightForm.addEventListener('submit', (e) => this.handleFormSubmit(e));

        // 搜索过滤
        [this.gradeFilter, this.quantityFilter, this.portFilter, this.dateFilter, this.regionFilter].forEach(filter => {
            filter.addEventListener('input', () => this.applyFilters());
        });

                this.clearFiltersBtn.addEventListener('click', () => this.clearFilters());

        // Pagination events
        this.prevPageBtn.addEventListener('click', () => this.changePage(-1));
        this.nextPageBtn.addEventListener('click', () => this.changePage(1));
                this.pageSizeSelect.addEventListener('change', () => this.changePageSize());

        // Import events
        this.importBtn.addEventListener('click', () => this.importFile.click());
        this.importFile.addEventListener('change', (e) => this.handleFileUpload(e));
    }

    loadRegions() {
        // 加载默认区域配置
        const defaultRegions = ['亚洲', '欧洲', '美洲', '中东'];
        const savedRegions = localStorage.getItem('regions');

        if (savedRegions) {
            this.regions = JSON.parse(savedRegions);
        } else {
            this.regions = defaultRegions;
            this.saveRegions();
        }

        this.updateRegionOptions();
        this.renderRegionList();
    }

    saveRegions() {
        localStorage.setItem('regions', JSON.stringify(this.regions));
    }

    updateRegionOptions() {
        const createOptions = (selectElement, defaultText) => {
            selectElement.innerHTML = `<option value="">${defaultText}</option>`;
            this.regions.forEach(region => {
                const option = document.createElement('option');
                option.value = region;
                option.textContent = region;
                selectElement.appendChild(option);
            });
        };

        // 更新表单中的区域选择器
        createOptions(this.loadRegionSelect, '请选择装港区域');
        createOptions(this.dischargeRegionSelect, '请选择卸港区域');

        // 更新搜索筛选中的区域选择器
        createOptions(this.regionFilter, '所有区域');
    }

    renderRegionList() {
        this.regionList.innerHTML = '';
        this.regions.forEach(region => {
            const regionItem = document.createElement('div');
            regionItem.className = 'region-item';
            regionItem.innerHTML = `
                <span class="region-name">${region}</span>
                <button class="delete-region-btn" onclick="freightManager.deleteRegion('${region}')">删除</button>
            `;
            this.regionList.appendChild(regionItem);
        });
    }

    openSettingsPanel() {
        this.settingsPanel.classList.add('open');
        this.renderRegionList();
    }

    closeSettingsPanel() {
        this.settingsPanel.classList.remove('open');
        this.newRegionInput.value = '';
    }

    addRegion() {
        const newRegion = this.newRegionInput.value.trim();
        if (newRegion && !this.regions.includes(newRegion)) {
            this.regions.push(newRegion);
            this.saveRegions();
            this.updateRegionOptions();
            this.renderRegionList();
            this.newRegionInput.value = '';
        } else if (this.regions.includes(newRegion)) {
            alert('该区域已存在！');
        }
    }

    deleteRegion(regionName) {
        if (confirm(`确定要删除区域"${regionName}"吗？`)) {
            this.regions = this.regions.filter(region => region !== regionName);
            this.saveRegions();
            this.updateRegionOptions();
            this.renderRegionList();
        }
    }

    async loadData() {
        try {
            const response = await fetch('/api/freight-rates');
            this.data = await response.json();

            // 添加更多模拟数据
            const additionalData = [
                {
                    id: 3,
                    vessel: "PACIFIC GLORY",
                    charterer: "EXXON",
                    laycan: "10-15 AUG",
                    load: "SINGAPORE",
                    discharge: "HAMBURG",
                    grade: "BIOS",
                    quantity: "12KT",
                    freight: "$25,000 PD",
                    status: "PENDING",
                    load_region: "亚洲",
                    discharge_region: "欧洲"
                },
                {
                    id: 4,
                    vessel: "ATLANTIC WAVE",
                    charterer: "TOTAL",
                    laycan: "15-25 AUG",
                    load: "ROTTERDAM",
                    discharge: "NEW YORK",
                    grade: "UCO",
                    quantity: "30KT",
                    freight: "Low $90 PMT",
                    status: "ACTIVE",
                    load_region: "欧洲",
                    discharge_region: "美洲"
                },
                {
                    id: 5,
                    vessel: "MIDDLE EAST STAR",
                    charterer: "ARAMCO",
                    laycan: "20-30 AUG",
                    load: "RAS TANURA",
                    discharge: "FUJAIRAH",
                    grade: "Sulphuric Acid",
                    quantity: "1.5 KT",
                    freight: "RNR",
                    status: "COMPLETED",
                    load_region: "中东",
                    discharge_region: "中东"
                },
                {
                    id: 6,
                    vessel: "EURO TRADER",
                    charterer: "VITOL",
                    laycan: "10-15 SEP",
                    load: "ANTWERP",
                    discharge: "SINGAPORE",
                    grade: "PROWAX 610",
                    quantity: "5KT",
                    freight: "Low $200's PMT",
                    status: "ACTIVE",
                    load_region: "欧洲",
                    discharge_region: "亚洲"
                },
                {
                    id: 7,
                    vessel: "ASIA PIONEER",
                    charterer: "SHELL",
                    laycan: "1-10 AUG",
                    load: "BUSAN",
                    discharge: "YOKOHAMA",
                    grade: "BIOS/LAURICS/PALMS",
                    quantity: "35KT",
                    freight: "$2.8M LS (2:2)",
                    status: "HISTORICAL",
                    load_region: "亚洲",
                    discharge_region: "亚洲"
                },
                {
                    id: 8,
                    vessel: "AMERICAN EAGLE",
                    charterer: "CHEVRON",
                    laycan: "2H JUL",
                    load: "GALVESTON",
                    discharge: "SANTOS",
                    grade: "MTBE",
                    quantity: "TCT",
                    freight: "$1.65M LS",
                    status: "HISTORICAL",
                    load_region: "美洲",
                    discharge_region: "美洲"
                }
            ];

            // Generate a large amount of historical data for pagination
            for (let i = 9; i <= 60; i++) {
                additionalData.push({
                    id: i,
                    vessel: `VESSEL ${i}`,
                    charterer: `CHARTERER ${i % 5}`,
                    laycan: `${i % 28 + 1}-${i % 28 + 3} JUN`,
                    load: `LOAD PORT ${i % 10}`,
                    discharge: `DISCHARGE PORT ${i % 10}`,
                    grade: `GRADE ${i % 7}`,
                    quantity: `${i * 2}KT`,
                    freight: `$${i * 100} PMT`,
                    status: "HISTORICAL",
                    load_region: this.regions[i % this.regions.length],
                    discharge_region: this.regions[(i + 1) % this.regions.length]
                });
            }

            this.data = [...this.data, ...additionalData];
            this.applyFilters();
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }

    switchView(view) {
        this.currentView = view;
        this.currentPage = 1; // Reset to first page on view switch

        // 更新按钮状态
        this.currentDataBtn.classList.toggle('active', view === 'current');
        this.historicalDataBtn.classList.toggle('active', view === 'historical');

        // Show/hide pagination
        if (view === 'historical') {
            this.paginationControls.style.display = 'flex';
        } else {
            this.paginationControls.style.display = 'none';
        }

        this.applyFilters();
    }

    applyFilters() {
        this.currentPage = 1; // Reset to first page on filter change
        let filtered = [...this.data];

        // 根据当前视图过滤
        if (this.currentView === 'current') {
            // "最新数据" is a subset of all data
            filtered = filtered.filter(item => this.isCurrentData(item.laycan));
        } else {
            // "历史数据" contains all data, so no initial view-based filtering is needed.
        }

        // 应用搜索过滤
        const gradeQuery = this.gradeFilter.value.toLowerCase();
        const quantityQuery = this.quantityFilter.value.toLowerCase();
        const portQuery = this.portFilter.value.toLowerCase();
        const dateQuery = this.dateFilter.value.toLowerCase();
        const regionQuery = this.regionFilter.value;

        if (gradeQuery) {
            filtered = filtered.filter(item =>
                item.grade.toLowerCase().includes(gradeQuery)
            );
        }

        if (quantityQuery) {
            filtered = filtered.filter(item =>
                item.quantity.toLowerCase().includes(quantityQuery)
            );
        }

        if (portQuery) {
            filtered = filtered.filter(item =>
                item.load.toLowerCase().includes(portQuery) ||
                item.discharge.toLowerCase().includes(portQuery)
            );
        }

        if (dateQuery) {
            filtered = filtered.filter(item =>
                item.laycan.toLowerCase().includes(dateQuery)
            );
        }

        if (regionQuery) {
            filtered = filtered.filter(item =>
                item.load_region === regionQuery || item.discharge_region === regionQuery
            );
        }

        this.filteredData = filtered;
        this.renderTable();
    }

    isCurrentData(laycan) {
        const monthMap = {
            'JAN': 0, 'FEB': 1, 'MAR': 2, 'APR': 3, 'MAY': 4, 'JUN': 5,
            'JUL': 6, 'AUG': 7, 'SEP': 8, 'OCT': 9, 'NOV': 10, 'DEC': 11
        };

        const today = new Date();
        today.setHours(0, 0, 0, 0); // Compare date part only
        const currentYear = today.getFullYear();

        const laycanUpper = laycan.toUpperCase();
        let laycanMonth = -1;
        let laycanDay = -1;

        // Find month from string
        for (const monthAbbr in monthMap) {
            if (laycanUpper.includes(monthAbbr)) {
                laycanMonth = monthMap[monthAbbr];
                break;
            }
        }

        if (laycanMonth === -1) return true; // Default to current if month is not parsed

        // Extract the latest day from string
        const dayMatch = laycanUpper.match(/(\d+)/g);
        if (dayMatch) {
            laycanDay = Math.max(...dayMatch.map(d => parseInt(d, 10)));
        } else if (laycanUpper.includes('H')) {
            // Handle cases like "2H JUL" (second half of July) -> approx end of month
            const tempDate = new Date(currentYear, laycanMonth + 1, 0);
            laycanDay = tempDate.getDate();
        } else {
            return true; // Default to current if no day is found
        }

        if (isNaN(laycanDay)) return true;

        const laycanEndDate = new Date(currentYear, laycanMonth, laycanDay);

        return laycanEndDate >= today;
    }

    renderTable() {
        this.tableBody.innerHTML = '';
        let dataToRender = this.filteredData;

        if (this.currentView === 'historical') {
            const startIndex = (this.currentPage - 1) * this.pageSize;
            const endIndex = startIndex + this.pageSize;
            dataToRender = this.filteredData.slice(startIndex, endIndex);
            this.updatePaginationInfo();
        }

        dataToRender.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.vessel}</td>
                <td>${item.charterer}</td>
                <td>${item.laycan}</td>
                <td>${item.load}</td>
                <td>${item.discharge}</td>
                <td>${item.grade}</td>
                <td>${item.quantity}</td>
                <td>${item.freight}</td>
                <td>${item.status}</td>
                <td>${item.load_region}</td>
                <td>${item.discharge_region}</td>
                <td>
                    <button class="edit-btn" onclick="freightManager.editItem(${item.id})">编辑</button>
                    <button class="delete-btn" onclick="freightManager.deleteItem(${item.id})">删除</button>
                </td>
            `;
            this.tableBody.appendChild(row);
        });
    }

    updatePaginationInfo() {
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);
        this.pageInfo.textContent = `第 ${this.currentPage} / ${totalPages || 1} 页 (共 ${this.filteredData.length} 条)`;

        this.prevPageBtn.disabled = this.currentPage === 1;
        this.nextPageBtn.disabled = this.currentPage === totalPages || totalPages === 0;
    }

    changePage(direction) {
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);
        const newPage = this.currentPage + direction;

        if (newPage >= 1 && newPage <= totalPages) {
            this.currentPage = newPage;
            this.renderTable();
        }
    }

    changePageSize() {
        this.pageSize = parseInt(this.pageSizeSelect.value, 10);
        this.currentPage = 1; // Reset to first page
        this.renderTable();
    }

    handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) {
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const text = e.target.result;
            this.parseCSV(text);
        };
        reader.readAsText(file);

        // Reset file input to allow re-uploading the same file
        event.target.value = '';
    }

    parseCSV(text) {
        const lines = text.split(/\r\n|\n/);
        if (lines.length < 2) {
            alert('CSV文件为空或格式不正确。');
            return;
        }

        const headers = lines[0].split(',').map(header => header.trim().toLowerCase());
        const expectedHeaders = [
            'vessel', 'charterer', 'laycan', 'load', 'discharge',
            'grade', 'quantity', 'freight', 'status', 'load_region', 'discharge_region'
        ];

        // Basic header validation
        const missingHeaders = expectedHeaders.filter(h => !headers.includes(h));
        if (missingHeaders.length > 0) {
            alert(`CSV文件缺少以下表头: ${missingHeaders.join(', ')}`);
            return;
        }

        const newEntries = [];
        const maxId = Math.max(0, ...this.data.map(item => item.id));

        for (let i = 1; i < lines.length; i++) {
            const line = lines[i];
            if (!line.trim()) continue;

            const values = line.split(',').map(value => value.trim());
            const entry = {};
            headers.forEach((header, index) => {
                entry[header] = values[index];
            });

            newEntries.push({
                id: maxId + i,
                ...entry
            });
        }

        if (newEntries.length > 0) {
            this.data = [...this.data, ...newEntries];
            this.applyFilters();
            alert(`${newEntries.length} 条记录导入成功!`);
        } else {
            alert('文件中未找到新记录。');
        }
    }

    clearFilters() {
        this.gradeFilter.value = '';
        this.quantityFilter.value = '';
        this.portFilter.value = '';
        this.dateFilter.value = '';
        this.regionFilter.value = '';
        this.applyFilters();
    }

    openPanel(editData = null) {
        this.editingId = editData ? editData.id : null;
        this.panelTitle.textContent = editData ? '编辑运价' : '新增运价';

        if (editData) {
            // 填充表单数据
            Object.keys(editData).forEach(key => {
                const input = document.getElementById(key);
                if (input && key !== 'id') {
                    input.value = editData[key];
                }
            });
        } else {
            this.freightForm.reset();
        }

        this.slidePanel.classList.add('open');
    }

    closePanel() {
        this.slidePanel.classList.remove('open');
        this.editingId = null;
        this.freightForm.reset();
    }

    async handleFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(this.freightForm);
        const data = Object.fromEntries(formData.entries());

        try {
            if (this.editingId) {
                // 更新现有数据
                const index = this.data.findIndex(item => item.id === this.editingId);
                if (index !== -1) {
                    this.data[index] = { ...this.data[index], ...data };
                }
            } else {
                // 添加新数据
                const newId = Math.max(...this.data.map(item => item.id)) + 1;
                this.data.push({ id: newId, ...data });
            }

            // 发送到服务器
            await fetch('/api/freight-rates', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            this.closePanel();
            this.applyFilters();

        } catch (error) {
            console.error('Error saving data:', error);
            alert('保存失败，请重试');
        }
    }

    editItem(id) {
        const item = this.data.find(item => item.id === id);
        if (item) {
            this.openPanel(item);
        }
    }

    deleteItem(id) {
        if (confirm('确定要删除这条记录吗？')) {
            this.data = this.data.filter(item => item.id !== id);
            this.applyFilters();
        }
    }
}

// 初始化应用
const freightManager = new FreightRateManager();
