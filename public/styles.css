* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    overflow-x: hidden;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    display: flex;
    flex-direction: column;
}

.container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px;
    min-height: 100vh;
}

header {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

header h1 {
    color: #2c3e50;
    font-size: 24px;
}

.header-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.settings-btn {
    background: #9b59b6;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s;
}

.settings-btn:hover {
    background: #8e44ad;
}

.import-btn {
    background: #f39c12;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s;
}

.import-btn:hover {
    background: #e67e22;
}

.data-toggle {
    display: flex;
    background: #ecf0f1;
    border-radius: 6px;
    overflow: hidden;
}

.toggle-btn {
    padding: 8px 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s;
}

.toggle-btn.active {
    background: #3498db;
    color: white;
}

.add-btn {
    background: #27ae60;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s;
}

.add-btn:hover {
    background: #219a52;
}

.search-section {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 10px;
    flex-shrink: 0;
}

.search-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: center;
}

.search-filters input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.clear-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    white-space: nowrap;
}

.clear-btn:hover {
    background: #c0392b;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: auto;
    flex: 1;
    min-height: 0;
}

table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1200px;
}

/* Pagination Controls */
.pagination-controls {
    display: none; /* Hidden by default */
    justify-content: flex-end;
    align-items: center;
    padding: 15px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 10px;
    flex-shrink: 0;
}

.pagination-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin: 0 5px;
    transition: background 0.3s;
}

.pagination-btn:hover {
    background: #2980b9;
}

.pagination-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

#pageInfo {
    margin: 0 15px;
    font-weight: 500;
    color: #2c3e50;
}

#pageSizeSelect {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-left: 15px;
}


th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
    white-space: nowrap;
}

th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
    z-index: 10;
}

tr:hover {
    background: #f8f9fa;
}

.edit-btn, .delete-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    margin-right: 5px;
}

.edit-btn {
    background: #3498db;
    color: white;
}

.delete-btn {
    background: #e74c3c;
    color: white;
}

/* 侧滑面板样式 */
.slide-panel {
    position: fixed;
    top: 0;
    right: -600px;
    width: 600px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    transition: right 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
}

.slide-panel.open {
    right: 0;
}

/* 设置面板样式 */
.settings-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    transition: right 0.3s ease;
    z-index: 1001;
    overflow-y: auto;
}

.settings-panel.open {
    right: 0;
}

.panel-content {
    padding: 20px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.panel-header h2 {
    color: #2c3e50;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #7f8c8d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #e74c3c;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.save-btn {
    background: #27ae60;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    flex: 1;
}

.save-btn:hover {
    background: #219a52;
}

.cancel-btn {
    background: #95a5a6;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    flex: 1;
}

.cancel-btn:hover {
    background: #7f8c8d;
}

.region-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 10px;
}

.region-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.region-item:last-child {
    border-bottom: none;
}

.region-name {
    font-weight: 500;
}

.delete-region-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.delete-region-btn:hover {
    background: #c0392b;
}

.add-region-form {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.add-region-input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.add-region-btn {
    background: #27ae60;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    white-space: nowrap;
}

.add-region-btn:hover {
    background: #219a52;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 5px;
    }

    header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
        padding: 10px;
    }

    .header-controls {
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .search-filters {
        grid-template-columns: 1fr;
    }

    .slide-panel, .settings-panel {
        width: 100%;
        right: -100%;
    }

    table {
        font-size: 12px;
    }

    th, td {
        padding: 6px;
    }
}
