console.log('Server script starting...');
const express = require('express');
const path = require('path');
const app = express();
const PORT = 3000;

// 设置静态文件目录
app.use(express.static(path.join(__dirname, 'public')));

// 解析JSON请求体
app.use(express.json());

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API路由 - 获取数据
app.get('/api/freight-rates', (req, res) => {
    // 这里可以连接数据库，现在返回模拟数据
    const mockData = [
        {
            id: 1,
            vessel: "OCEAN STAR",
            charterer: "SHELL",
            laycan: "24-26 AUG",
            load: "SINGAPORE",
            discharge: "ROTTERDAM",
            grade: "MTBE",
            quantity: "35-40KT",
            freight: "$140 PMT",
            status: "ACTIVE",
            load_region: "亚洲",
            discharge_region: "欧洲"
        },
        {
            id: 2,
            vessel: "SEA WIND",
            charterer: "BP",
            laycan: "09-11 AUG",
            load: "HOUSTON",
            discharge: "ANTWERP",
            grade: "SAF",
            quantity: "35KT",
            freight: "$2.2M LS",
            status: "ACTIVE",
            load_region: "美洲",
            discharge_region: "欧洲"
        }
    ];
    res.json(mockData);
});

// API路由 - 保存数据
app.post('/api/freight-rates', (req, res) => {
    // 这里可以保存到数据库
    console.log('Received data:', req.body);
    res.json({ success: true, message: 'Data saved successfully' });
});

app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});
