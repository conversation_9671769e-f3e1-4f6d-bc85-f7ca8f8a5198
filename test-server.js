console.log('Starting test server...');
try {
    const http = require('http');

    const server = http.createServer((req, res) => {
        res.writeHead(200, { 'Content-Type': 'text/plain' });
        res.end('Test server is running!\n');
    });

    const PORT = 3002;
    server.listen(PORT, () => {
        console.log(`Test server listening on http://localhost:${PORT}`);
    });

} catch (e) {
    console.error('Error starting test server:', e);
}
